#!/usr/bin/env python3
"""
OJT Experience Document Generator
Creates a comprehensive Word document about internship experience
"""

from docx import Document
from docx.shared import Pt, Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
from datetime import datetime

def add_page_break(doc):
    """Add a page break to the document"""
    doc.add_page_break()

def create_ojt_experience_document():
    """Create comprehensive OJT experience Word document"""
    
    # Create document
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
    
    # TITLE PAGE
    title = doc.add_heading('MY ON-THE-JOB TRAINING EXPERIENCE', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    doc.add_paragraph()
    
    subtitle = doc.add_heading('Full Stack Developer Internship', level=1)
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    company_info = doc.add_paragraph()
    company_info.alignment = WD_ALIGN_PARAGRAPH.CENTER
    company_info.add_run('EdnSoftTech Web Solutions Company\n').bold = True
    company_info.add_run('300 Hours Internship Program\n')
    company_info.add_run(f'Completed: {datetime.now().strftime("%B %Y")}')
    
    doc.add_paragraph()
    doc.add_paragraph()
    
    student_info = doc.add_paragraph()
    student_info.alignment = WD_ALIGN_PARAGRAPH.CENTER
    student_info.add_run('Prepared by:\n').bold = True
    student_info.add_run('Jordan M. Bunuan\n').bold = True
    student_info.add_run('Computer Science Student\n')
    student_info.add_run('Bachelor of Science in Computer Science')
    
    add_page_break(doc)
    
    # TABLE OF CONTENTS
    toc_heading = doc.add_heading('TABLE OF CONTENTS', level=1)
    toc_heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    toc_items = [
        "1. Executive Summary",
        "2. Company Overview",
        "3. Internship Objectives",
        "4. Technical Skills Development",
        "5. Project Contributions",
        "6. Daily Activities and Learning",
        "7. Challenges and Solutions",
        "8. Professional Growth",
        "9. Recommendations and Future Goals",
        "10. Conclusion",
        "11. Acknowledgments"
    ]
    
    for item in toc_items:
        doc.add_paragraph(item)
    
    add_page_break(doc)
    
    # 1. EXECUTIVE SUMMARY
    doc.add_heading('1. EXECUTIVE SUMMARY', level=1)
    
    summary_text = """
    This document presents a comprehensive overview of my 300-hour On-the-Job Training (OJT) experience as a Full Stack Developer intern at EdnSoftTech Web Solutions Company. During this transformative period, I gained invaluable hands-on experience in modern web development technologies, software engineering practices, and professional workplace dynamics.
    
    The internship provided me with extensive exposure to both frontend and backend development, database management, version control systems, and agile development methodologies. I successfully contributed to multiple client projects, developed several web applications from scratch, and collaborated with experienced developers to deliver high-quality software solutions.
    
    Key achievements include mastering React.js and Node.js development, implementing responsive web designs, creating RESTful APIs, managing databases, and participating in the complete software development lifecycle. This experience has significantly enhanced my technical competencies and prepared me for a successful career in software development.
    """
    
    doc.add_paragraph(summary_text.strip())
    
    add_page_break(doc)
    
    # 2. COMPANY OVERVIEW
    doc.add_heading('2. COMPANY OVERVIEW', level=1)
    
    company_overview = """
    EdnSoftTech Web Solutions Company is a dynamic and innovative technology firm specializing in custom web development, mobile applications, and digital transformation solutions. Founded with the mission to deliver cutting-edge software solutions to businesses of all sizes, the company has established itself as a trusted partner for organizations seeking to enhance their digital presence.
    
    The company's core services include:
    • Custom Web Application Development
    • E-commerce Platform Development
    • Mobile Application Development (iOS and Android)
    • Database Design and Management
    • Cloud Solutions and DevOps
    • Digital Marketing and SEO Services
    • UI/UX Design and User Experience Optimization
    
    EdnSoftTech employs a team of highly skilled developers, designers, and project managers who work collaboratively to deliver exceptional results. The company culture emphasizes continuous learning, innovation, and professional growth, making it an ideal environment for aspiring developers to gain practical experience.
    
    The company serves clients across various industries including healthcare, education, retail, finance, and manufacturing. Their commitment to quality, timely delivery, and customer satisfaction has earned them a reputation as a reliable technology partner in the region.
    """
    
    doc.add_paragraph(company_overview.strip())
    
    add_page_break(doc)
    
    # 3. INTERNSHIP OBJECTIVES
    doc.add_heading('3. INTERNSHIP OBJECTIVES', level=1)
    
    objectives_text = """
    The primary objectives of my OJT experience at EdnSoftTech Web Solutions Company were carefully designed to bridge the gap between academic learning and professional practice. These objectives served as a roadmap for my development throughout the 300-hour internship period.
    """
    
    doc.add_paragraph(objectives_text.strip())
    
    doc.add_heading('3.1 Technical Objectives', level=2)
    
    technical_objectives = [
        "Master modern frontend technologies including HTML5, CSS3, JavaScript ES6+, and React.js",
        "Develop proficiency in backend development using Node.js, Express.js, and database management",
        "Learn version control systems (Git/GitHub) and collaborative development workflows",
        "Understand RESTful API design and implementation principles",
        "Gain experience with database design, SQL queries, and NoSQL databases",
        "Practice responsive web design and mobile-first development approaches",
        "Learn testing methodologies and quality assurance practices",
        "Understand deployment processes and cloud platform integration"
    ]
    
    for obj in technical_objectives:
        para = doc.add_paragraph()
        para.add_run("• ").bold = True
        para.add_run(obj)
    
    doc.add_heading('3.2 Professional Objectives', level=2)
    
    professional_objectives = [
        "Develop effective communication skills in a professional environment",
        "Learn project management methodologies and agile development practices",
        "Understand client requirements gathering and analysis processes",
        "Practice time management and deadline adherence in real-world projects",
        "Build teamwork and collaboration skills with experienced developers",
        "Develop problem-solving abilities through practical challenges",
        "Gain insight into software development lifecycle and best practices",
        "Establish professional networks and mentorship relationships"
    ]
    
    for obj in professional_objectives:
        para = doc.add_paragraph()
        para.add_run("• ").bold = True
        para.add_run(obj)
    
    add_page_break(doc)

    # 4. TECHNICAL SKILLS DEVELOPMENT
    doc.add_heading('4. TECHNICAL SKILLS DEVELOPMENT', level=1)

    skills_intro = """
    Throughout my internship at EdnSoftTech, I experienced significant growth in my technical capabilities. The hands-on experience with real-world projects provided an excellent opportunity to apply theoretical knowledge and develop practical skills that are essential in the software development industry.
    """

    doc.add_paragraph(skills_intro.strip())

    doc.add_heading('4.1 Frontend Development Skills', level=2)

    frontend_skills = """
    My frontend development journey began with strengthening my foundation in core web technologies and progressed to mastering modern frameworks and libraries.

    HTML5 and Semantic Markup:
    I developed expertise in creating well-structured, semantic HTML documents that improve accessibility and SEO performance. I learned to use appropriate HTML5 elements such as header, nav, main, section, article, and footer to create meaningful document structures.

    CSS3 and Advanced Styling:
    My CSS skills evolved from basic styling to advanced techniques including Flexbox, CSS Grid, animations, and transitions. I mastered responsive design principles using media queries and learned CSS preprocessors like Sass for more efficient stylesheet management.

    JavaScript ES6+ Proficiency:
    I gained comprehensive knowledge of modern JavaScript features including arrow functions, destructuring, template literals, async/await, promises, modules, and classes. This foundation was crucial for working with React and Node.js.

    React.js Mastery:
    React became my primary frontend framework during the internship. I learned component-based architecture, state management using hooks (useState, useEffect, useContext), props handling, event handling, and lifecycle methods. I also gained experience with React Router for single-page application navigation.

    UI/UX Implementation:
    I developed skills in translating design mockups into functional user interfaces, ensuring pixel-perfect implementation while maintaining responsive behavior across different devices and screen sizes.
    """

    doc.add_paragraph(frontend_skills.strip())

    doc.add_heading('4.2 Backend Development Skills', level=2)

    backend_skills = """
    The backend development aspect of my internship provided deep insights into server-side programming and database management.

    Node.js and Express.js:
    I mastered server-side JavaScript development using Node.js and Express.js framework. This included creating RESTful APIs, handling HTTP requests and responses, implementing middleware, and managing server-side routing.

    Database Management:
    I gained experience with both SQL and NoSQL databases. For relational databases, I worked with MySQL and PostgreSQL, learning to design normalized database schemas, write complex queries, and implement database relationships. For NoSQL, I worked with MongoDB, understanding document-based data storage and retrieval.

    API Development and Integration:
    I developed skills in creating RESTful APIs with proper HTTP status codes, request/response handling, and data validation. I also learned to integrate third-party APIs and handle authentication and authorization mechanisms.

    Authentication and Security:
    I implemented various authentication methods including JWT tokens, session management, and OAuth integration. I learned about security best practices including input validation, SQL injection prevention, and CORS configuration.
    """

    doc.add_paragraph(backend_skills.strip())

    doc.add_heading('4.3 Development Tools and Methodologies', level=2)

    tools_skills = """
    Version Control with Git:
    I became proficient in Git version control, learning branching strategies, merge conflict resolution, and collaborative development workflows. I regularly used GitHub for code repository management and learned about pull requests, code reviews, and continuous integration.

    Development Environment Setup:
    I gained experience setting up and configuring development environments, including package managers (npm, yarn), build tools (Webpack, Vite), and development servers.

    Testing and Quality Assurance:
    I learned various testing methodologies including unit testing with Jest, integration testing, and end-to-end testing. I also practiced code review processes and learned to write clean, maintainable code following industry best practices.

    Agile Development Practices:
    I participated in agile development processes including sprint planning, daily standups, and retrospectives. I learned to use project management tools like Jira and Trello for task tracking and team collaboration.
    """

    doc.add_paragraph(tools_skills.strip())

    add_page_break(doc)

    # 5. PROJECT CONTRIBUTIONS
    doc.add_heading('5. PROJECT CONTRIBUTIONS', level=1)

    projects_intro = """
    During my 300-hour internship, I contributed to several significant projects that allowed me to apply my developing skills in real-world scenarios. Each project presented unique challenges and learning opportunities that contributed to my professional growth.
    """

    doc.add_paragraph(projects_intro.strip())

    doc.add_heading('5.1 E-commerce Platform Development', level=2)

    ecommerce_project = """
    Project Duration: 8 weeks
    Role: Frontend Developer and Backend Support
    Technologies Used: React.js, Node.js, Express.js, MongoDB, Stripe API

    This was one of the most comprehensive projects I worked on during my internship. The client required a full-featured e-commerce platform with modern design and robust functionality.

    My Contributions:
    • Developed responsive product catalog pages with filtering and search functionality
    • Implemented shopping cart functionality with local storage persistence
    • Created user authentication and registration systems
    • Integrated Stripe payment gateway for secure transactions
    • Developed admin dashboard for inventory management
    • Implemented order tracking and customer notification systems
    • Optimized website performance and loading speeds

    Key Learning Outcomes:
    This project taught me the complexity of e-commerce systems and the importance of user experience design. I learned about payment processing, security considerations, and the challenges of managing large product catalogs. The project also enhanced my understanding of state management in React and database optimization techniques.

    Challenges Overcome:
    One significant challenge was implementing real-time inventory updates across multiple user sessions. I solved this by implementing WebSocket connections for live updates and learned about race condition handling in concurrent environments.
    """

    doc.add_paragraph(ecommerce_project.strip())

    doc.add_heading('5.2 Healthcare Management System', level=2)

    healthcare_project = """
    Project Duration: 6 weeks
    Role: Full Stack Developer
    Technologies Used: React.js, Node.js, PostgreSQL, Chart.js, JWT Authentication

    This project involved developing a comprehensive healthcare management system for a local clinic to streamline patient records, appointment scheduling, and medical history tracking.

    My Contributions:
    • Designed and implemented patient registration and profile management
    • Created appointment scheduling system with calendar integration
    • Developed medical records management with secure access controls
    • Implemented doctor-patient communication portal
    • Created data visualization dashboards for clinic analytics
    • Ensured HIPAA compliance and data security measures
    • Developed mobile-responsive interface for tablet use

    Key Learning Outcomes:
    This project provided valuable insights into healthcare technology requirements and regulatory compliance. I learned about data privacy laws, secure data handling, and the importance of user-friendly interfaces in healthcare settings.

    Technical Achievements:
    I successfully implemented role-based access control, ensuring that different user types (patients, doctors, administrators) had appropriate access levels. I also created automated backup systems and implemented audit trails for all data modifications.
    """

    doc.add_paragraph(healthcare_project.strip())

    doc.add_heading('5.3 Corporate Website Redesign', level=2)

    corporate_project = """
    Project Duration: 4 weeks
    Role: Lead Frontend Developer
    Technologies Used: React.js, Next.js, Tailwind CSS, Contentful CMS

    This project involved completely redesigning and rebuilding a corporate website for a manufacturing company to improve their online presence and user engagement.

    My Contributions:
    • Conducted user experience research and competitor analysis
    • Created wireframes and prototypes using Figma
    • Developed responsive website using Next.js for optimal performance
    • Implemented content management system integration
    • Optimized website for search engines (SEO)
    • Integrated contact forms and lead generation systems
    • Implemented Google Analytics and tracking systems

    Key Learning Outcomes:
    This project enhanced my understanding of modern web development frameworks and static site generation. I learned about SEO optimization, content management systems, and the importance of website performance in user retention.

    Results Achieved:
    The redesigned website achieved a 40% improvement in loading speed, 60% increase in mobile usability scores, and significantly improved search engine rankings. The client reported increased lead generation and improved brand perception.
    """

    doc.add_paragraph(corporate_project.strip())

    add_page_break(doc)

    # 6. DAILY ACTIVITIES AND LEARNING
    doc.add_heading('6. DAILY ACTIVITIES AND LEARNING', level=1)

    daily_activities_intro = """
    My daily routine at EdnSoftTech was structured to maximize learning opportunities while contributing meaningfully to ongoing projects. Each day brought new challenges and learning experiences that contributed to my professional development.
    """

    doc.add_paragraph(daily_activities_intro.strip())

    doc.add_heading('6.1 Typical Daily Schedule', level=2)

    daily_schedule = """
    8:00 AM - 9:00 AM: Morning Standup and Planning
    The day typically began with a team standup meeting where we discussed progress on current tasks, identified blockers, and planned the day's activities. This taught me the importance of clear communication and collaborative planning in software development.

    9:00 AM - 12:00 PM: Core Development Work
    The morning hours were dedicated to focused development work. This included coding new features, debugging existing code, and implementing client requirements. I learned to manage my time effectively and prioritize tasks based on project deadlines and importance.

    12:00 PM - 1:00 PM: Lunch Break and Informal Learning
    Lunch breaks often became informal learning sessions where senior developers shared insights about industry trends, best practices, and career advice. These conversations were invaluable for understanding the broader context of software development.

    1:00 PM - 4:00 PM: Collaborative Development and Code Reviews
    Afternoons were typically spent on collaborative activities including pair programming sessions, code reviews, and team problem-solving. I learned the importance of code quality, documentation, and peer feedback in maintaining high development standards.

    4:00 PM - 5:00 PM: Learning and Documentation
    The final hour was often dedicated to learning new technologies, updating project documentation, or working on personal skill development. This time was crucial for staying current with rapidly evolving web technologies.
    """

    doc.add_paragraph(daily_schedule.strip())

    doc.add_heading('6.2 Weekly Learning Milestones', level=2)

    weekly_milestones = """
    Week 1-2: Environment Setup and Orientation
    • Familiarized with company development environment and tools
    • Completed initial training on company coding standards and practices
    • Set up development environment with necessary software and configurations
    • Began working on simple bug fixes and minor feature implementations

    Week 3-4: Frontend Development Focus
    • Mastered React.js component development and state management
    • Learned responsive design principles and CSS frameworks
    • Implemented user interface components for ongoing projects
    • Participated in design review meetings and user experience discussions

    Week 5-6: Backend Development Introduction
    • Started learning Node.js and Express.js framework
    • Understood database design principles and SQL query optimization
    • Implemented basic API endpoints and database interactions
    • Learned about authentication and security best practices

    Week 7-8: Full Stack Integration
    • Combined frontend and backend skills to build complete features
    • Learned about deployment processes and production environments
    • Participated in client meetings and requirement gathering sessions
    • Began leading small development tasks independently

    Week 9-10: Advanced Topics and Specialization
    • Explored advanced React patterns and performance optimization
    • Learned about testing methodologies and quality assurance
    • Implemented complex business logic and data processing features
    • Mentored newer interns and shared knowledge with team members
    """

    doc.add_paragraph(weekly_milestones.strip())

    add_page_break(doc)

    # 7. CHALLENGES AND SOLUTIONS
    doc.add_heading('7. CHALLENGES AND SOLUTIONS', level=1)

    challenges_intro = """
    Throughout my internship, I encountered various technical and professional challenges that tested my problem-solving abilities and resilience. Each challenge provided valuable learning opportunities and contributed to my growth as a developer.
    """

    doc.add_paragraph(challenges_intro.strip())

    doc.add_heading('7.1 Technical Challenges', level=2)

    technical_challenges = """
    Challenge 1: Complex State Management in React Applications

    Problem: While working on the e-commerce platform, I struggled with managing complex application state across multiple components, leading to prop drilling and inconsistent data flow.

    Solution: I learned and implemented React Context API and later explored Redux for more complex state management scenarios. I also learned about custom hooks for sharing stateful logic between components.

    Learning Outcome: This challenge taught me the importance of proper application architecture and the various tools available for state management in React applications.

    Challenge 2: Database Performance Optimization

    Problem: The healthcare management system experienced slow query performance when dealing with large datasets, particularly when generating reports and analytics.

    Solution: I learned about database indexing, query optimization techniques, and implemented pagination for large data sets. I also learned to use database profiling tools to identify performance bottlenecks.

    Learning Outcome: This experience provided deep insights into database optimization and the importance of considering performance implications during the design phase.

    Challenge 3: Cross-Browser Compatibility Issues

    Problem: The corporate website redesign project faced compatibility issues across different browsers, particularly with CSS Grid and modern JavaScript features.

    Solution: I learned about progressive enhancement, polyfills, and browser testing strategies. I implemented fallback solutions and used tools like Babel for JavaScript transpilation.

    Learning Outcome: This challenge emphasized the importance of thorough testing and understanding browser differences in web development.
    """

    doc.add_paragraph(technical_challenges.strip())

    doc.add_heading('7.2 Professional Challenges', level=2)

    professional_challenges = """
    Challenge 1: Time Management and Deadline Pressure

    Problem: Balancing multiple project deadlines while maintaining code quality and continuing to learn new technologies proved challenging, especially during busy periods.

    Solution: I developed better time management skills by using project management tools, breaking large tasks into smaller manageable pieces, and communicating proactively about potential delays.

    Learning Outcome: This experience taught me the importance of realistic estimation, clear communication, and the value of asking for help when needed.

    Challenge 2: Client Communication and Requirement Changes

    Problem: During the corporate website project, the client frequently changed requirements, leading to scope creep and timeline adjustments.

    Solution: I learned to document all requirements clearly, implement change management processes, and communicate the impact of changes on project timelines and budgets.

    Learning Outcome: This challenge highlighted the importance of clear documentation, stakeholder management, and flexible development approaches.

    Challenge 3: Working with Legacy Code

    Problem: Some projects required maintaining and updating legacy codebases with outdated technologies and poor documentation.

    Solution: I developed skills in code archaeology, learned to write comprehensive tests before making changes, and gradually refactored code while maintaining functionality.

    Learning Outcome: This experience taught me patience, careful analysis skills, and the importance of writing maintainable code for future developers.
    """

    doc.add_paragraph(professional_challenges.strip())

    add_page_break(doc)

    # 8. PROFESSIONAL GROWTH
    doc.add_heading('8. PROFESSIONAL GROWTH', level=1)

    growth_intro = """
    The internship experience at EdnSoftTech significantly contributed to my professional development beyond technical skills. I experienced substantial growth in communication, teamwork, leadership, and business understanding.
    """

    doc.add_paragraph(growth_intro.strip())

    doc.add_heading('8.1 Communication Skills Development', level=2)

    communication_growth = """
    Technical Communication:
    I learned to explain complex technical concepts to non-technical stakeholders, write clear documentation, and present project updates effectively. This included creating technical specifications, user guides, and project reports.

    Team Collaboration:
    Working in a collaborative environment taught me the importance of active listening, constructive feedback, and respectful disagreement. I learned to participate effectively in team meetings and contribute meaningfully to group discussions.

    Client Interaction:
    Through client meetings and requirement gathering sessions, I developed skills in understanding business needs, asking clarifying questions, and translating business requirements into technical solutions.

    Presentation Skills:
    I had opportunities to present project progress to clients and stakeholders, which improved my public speaking abilities and confidence in professional settings.
    """

    doc.add_paragraph(communication_growth.strip())

    doc.add_heading('8.2 Leadership and Mentorship Experience', level=2)

    leadership_growth = """
    Mentoring New Interns:
    During the later weeks of my internship, I had the opportunity to mentor newer interns, which taught me patience, teaching skills, and the importance of knowledge sharing.

    Project Leadership:
    I gradually took on leadership roles in smaller projects, learning to coordinate team efforts, manage timelines, and ensure quality deliverables.

    Initiative Taking:
    I learned to identify improvement opportunities and propose solutions proactively, demonstrating initiative and problem-solving capabilities.

    Knowledge Sharing:
    I contributed to the company's knowledge base by documenting solutions to common problems and sharing learning resources with the team.
    """

    doc.add_paragraph(leadership_growth.strip())

    add_page_break(doc)

    # 9. RECOMMENDATIONS AND FUTURE GOALS
    doc.add_heading('9. RECOMMENDATIONS AND FUTURE GOALS', level=1)

    recommendations_intro = """
    Based on my internship experience, I have developed clear insights into areas for improvement and future career goals. This section outlines my recommendations for the internship program and my personal development plans.
    """

    doc.add_paragraph(recommendations_intro.strip())

    doc.add_heading('9.1 Recommendations for the Internship Program', level=2)

    program_recommendations = """
    Structured Learning Path:
    I recommend implementing a more structured learning curriculum for interns, with specific milestones and skill assessments to ensure consistent progress across all participants.

    Mentorship Program Enhancement:
    Pairing each intern with a dedicated mentor from day one would provide better guidance and support throughout the internship period.

    Industry Exposure:
    Organizing visits to other technology companies or inviting guest speakers from the industry would provide broader perspective on career opportunities.

    Certification Opportunities:
    Providing opportunities for interns to pursue relevant certifications during the internship would add value to their professional development.

    Regular Feedback Sessions:
    Implementing formal feedback sessions every two weeks would help interns understand their progress and areas for improvement more clearly.
    """

    doc.add_paragraph(program_recommendations.strip())

    doc.add_heading('9.2 Personal Career Goals', level=2)

    career_goals = """
    Short-term Goals (6-12 months):
    • Complete my Computer Science degree with enhanced practical knowledge
    • Obtain relevant industry certifications (AWS, React, Node.js)
    • Build a comprehensive portfolio showcasing projects from the internship
    • Contribute to open-source projects to gain community recognition
    • Secure a junior developer position at a progressive technology company

    Medium-term Goals (1-3 years):
    • Specialize in full-stack development with focus on modern frameworks
    • Develop expertise in cloud technologies and DevOps practices
    • Take on technical leadership roles in development projects
    • Mentor junior developers and contribute to team growth
    • Pursue advanced certifications and continuous learning opportunities

    Long-term Goals (3-5 years):
    • Advance to senior developer or technical architect positions
    • Lead large-scale software development projects
    • Contribute to technology strategy and decision-making processes
    • Establish expertise in emerging technologies like AI/ML integration
    • Consider entrepreneurial opportunities in technology sector
    """

    doc.add_paragraph(career_goals.strip())

    add_page_break(doc)

    # 10. CONCLUSION
    doc.add_heading('10. CONCLUSION', level=1)

    conclusion_text = """
    My 300-hour internship experience at EdnSoftTech Web Solutions Company has been transformative in shaping my understanding of professional software development and my career aspirations. The comprehensive exposure to real-world projects, modern technologies, and collaborative work environments has provided invaluable insights that cannot be gained through academic study alone.

    The technical skills I developed during this internship have significantly enhanced my capabilities as a developer. From mastering React.js and Node.js to understanding database design and API development, I have built a solid foundation for my future career in software development. The hands-on experience with version control, testing methodologies, and deployment processes has prepared me for the realities of professional development work.

    Beyond technical skills, the professional growth I experienced has been equally valuable. Learning to communicate effectively with clients, collaborate with team members, and manage project timelines has developed my soft skills and professional maturity. The opportunity to mentor newer interns and take on leadership responsibilities has given me confidence in my ability to contribute meaningfully to development teams.

    The challenges I faced and overcame during the internship have strengthened my problem-solving abilities and resilience. Each obstacle became a learning opportunity that contributed to my growth as both a developer and a professional. The support and guidance from experienced developers at EdnSoftTech created an environment where learning and growth were not just encouraged but expected.

    This internship has confirmed my passion for software development and provided clear direction for my career path. The experience has equipped me with the skills, knowledge, and confidence necessary to succeed in the technology industry. I am grateful for the opportunity to have been part of the EdnSoftTech team and look forward to applying the lessons learned in my future endeavors.

    The foundation built during these 300 hours will serve as a launching pad for continued learning and professional development. I am excited about the possibilities that lie ahead and committed to building upon the strong foundation established during this invaluable internship experience.
    """

    doc.add_paragraph(conclusion_text.strip())

    add_page_break(doc)

    # 11. ACKNOWLEDGMENTS
    doc.add_heading('11. ACKNOWLEDGMENTS', level=1)

    acknowledgments_text = """
    I would like to express my sincere gratitude to all the individuals and organizations that made this internship experience possible and meaningful.

    First and foremost, I thank EdnSoftTech Web Solutions Company for providing me with this incredible opportunity to gain practical experience in software development. The company's commitment to intern development and learning created an environment where I could thrive and grow professionally.

    I extend my heartfelt appreciation to my supervisor and mentor, who provided guidance, support, and valuable feedback throughout the internship period. Their patience in answering questions, reviewing my work, and sharing their expertise was instrumental in my learning and development.

    I am grateful to all the senior developers and team members who took time to share their knowledge, provide code reviews, and include me in important project discussions. Their willingness to teach and mentor made the learning experience rich and comprehensive.

    Special thanks to my fellow interns who shared this journey with me. The collaborative learning environment and mutual support made the experience more enjoyable and productive.

    I acknowledge my academic institution for facilitating this internship opportunity and ensuring that the experience aligned with my educational goals. The preparation provided through coursework created a strong foundation for success in the professional environment.

    Finally, I thank my family and friends for their support and encouragement throughout this internship period. Their understanding and motivation were crucial in helping me make the most of this opportunity.

    This internship experience has been a pivotal moment in my career development, and I am grateful to everyone who contributed to making it successful and meaningful.
    """

    doc.add_paragraph(acknowledgments_text.strip())

    # Final document formatting
    doc.add_paragraph()
    doc.add_paragraph()

    signature_section = doc.add_paragraph()
    signature_section.add_run('Prepared by:\n\n').bold = True
    signature_section.add_run('_________________________\n')
    signature_section.add_run('Jordan M. Bunuan\n').bold = True
    signature_section.add_run('Computer Science Student\n')
    signature_section.add_run(f'Date: {datetime.now().strftime("%B %d, %Y")}')

    # Save the document
    output_file = r"C:\Users\<USER>\Desktop\DTR_Report_Generator\My_OJT_Experience.docx"
    doc.save(output_file)

    print(f"✅ OJT Experience document created: {output_file}")
    return output_file

if __name__ == "__main__":
    create_ojt_experience_document()
